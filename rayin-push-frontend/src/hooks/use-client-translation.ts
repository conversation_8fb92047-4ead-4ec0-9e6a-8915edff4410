'use client'

import { useEffect, useState } from 'react'
import { useLocale } from './use-locale'

export function useClientTranslation() {
  const { locale } = useLocale()
  const [translations, setTranslations] = useState<Record<string, string>>({})
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadTranslations = async () => {
      try {
        setIsLoading(true)
        const translationModule = await import(`@/locales/${locale}.json`)
        setTranslations(translationModule.default || translationModule)
      } catch (error) {
        console.error(`Failed to load translations for ${locale}:`, error)
        setTranslations({})
      } finally {
        setIsLoading(false)
      }
    }

    loadTranslations()
  }, [locale])

  const t = (key: string, fallback?: string): string => {
    return translations[key] || fallback || key
  }

  return { t, locale, isLoading }
}