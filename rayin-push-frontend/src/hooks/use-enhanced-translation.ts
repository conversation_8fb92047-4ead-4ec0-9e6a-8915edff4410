'use client'

import { useC<PERSON>back, useEffect, useMemo, useState } from 'react'
import { useLocale } from './use-locale'
import type { SupportedLocale } from '@/types/i18n'

// Enhanced translation cache with LRU eviction
class TranslationCache {
  private cache = new Map<string, any>()
  private accessOrder = new Map<string, number>()
  private maxSize: number = 50
  private accessCounter = 0

  get(key: string): any {
    const value = this.cache.get(key)
    if (value !== undefined) {
      this.accessOrder.set(key, ++this.accessCounter)
    }
    return value
  }

  set(key: string, value: any): void {
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evictLRU()
    }
    
    this.cache.set(key, value)
    this.accessOrder.set(key, ++this.accessCounter)
  }

  private evictLRU(): void {
    let oldestKey = ''
    let oldestAccess = Infinity
    
    for (const [key, access] of this.accessOrder) {
      if (access < oldestAccess) {
        oldestAccess = access
        oldestKey = key
      }
    }
    
    if (oldestKey) {
      this.cache.delete(oldestKey)
      this.accessOrder.delete(oldestKey)
    }
  }

  clear(): void {
    this.cache.clear()
    this.accessOrder.clear()
    this.accessCounter = 0
  }
}

// Global instances
const translationCache = new TranslationCache()
const loadingPromises = new Map<string, Promise<any>>()

// Translation options
interface TranslationOptions {
  count?: number
  [key: string]: any
}

// Enhanced useTypedTranslation hook
export function useTypedTranslation(
  options: {
    preload?: boolean
    fallback?: Record<string, string>
  } = {}
): {
  t: (
    key: string,
    opts?: string | TranslationOptions
  ) => string
  locale: SupportedLocale
  isLoading: boolean
  isReady: boolean
  error: string | null
} {
  const { locale } = useLocale()
  const [translations, setTranslations] = useState<Record<string, string> | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load translations with enhanced error handling
  const loadTranslations = useCallback(async (locale: string) => {
    const cacheKey = locale

    // Check cache first
    const cached = translationCache.get(cacheKey)
    if (cached) {
      setTranslations(cached)
      setIsLoading(false)
      setError(null)
      return cached
    }

    // Check if already loading
    if (loadingPromises.has(cacheKey)) {
      try {
        const result = await loadingPromises.get(cacheKey)
        setTranslations(result)
        setIsLoading(false)
        setError(null)
        return result
      } catch (err) {
        setError(`Failed to load ${cacheKey}`)
        setIsLoading(false)
        return null
      }
    }

    // Create loading promise
    const loadingPromise = (async () => {
      try {
        const translationModule = await import(`@/locales/${locale}.json`)
        const loadedTranslations = translationModule.default || translationModule

        // Cache the result
        translationCache.set(cacheKey, loadedTranslations)

        return loadedTranslations
      } catch (err) {
        console.error(`Failed to load translations for ${locale}:`, err)

        // Try fallback locale
        if (locale !== 'zh') {
          try {
            const fallbackModule = await import(`@/locales/zh.json`)
            const fallbackTranslations = fallbackModule.default || fallbackModule
            console.warn(`Using fallback translations for ${locale}`)
            return fallbackTranslations
          } catch (fallbackErr) {
            console.error(`Fallback also failed:`, fallbackErr)
          }
        }

        throw err
      }
    })()

    loadingPromises.set(cacheKey, loadingPromise)

    try {
      const result = await loadingPromise
      setTranslations(result)
      setError(null)
      return result
    } catch (err) {
      setError(`Failed to load ${cacheKey}`)
      // Use fallback if provided
      if (options.fallback) {
        setTranslations(options.fallback)
      }
      return null
    } finally {
      setIsLoading(false)
      loadingPromises.delete(cacheKey)
    }
  }, [options.fallback])

  // Load translations when locale changes
  useEffect(() => {
    setIsLoading(true)
    setError(null)
    loadTranslations(locale)
  }, [locale, loadTranslations])

  // Enhanced translation function with interpolation and pluralization
  const t = useCallback((
    key: string,
    opts?: string | TranslationOptions
  ): string => {
    if (!translations) {
      if (typeof opts === 'string') {
        return opts
      }
      return key
    }

    let translation = translations[key]

    if (!translation) {
      if (typeof opts === 'string') {
        return opts
      }
      return key
    }

    // Handle interpolation and pluralization
    if (typeof opts === 'object' && opts !== null) {
      // Simple pluralization
      if (typeof opts.count === 'number') {
        // This is a simplified version - in production you'd use a proper pluralization library
        if (opts.count === 0 && translation.includes('{{zero}}')) {
          translation = translation.replace(/\{\{zero\}\}(.*?)\{\{\/zero\}\}/g, '$1')
        } else if (opts.count === 1 && translation.includes('{{one}}')) {
          translation = translation.replace(/\{\{one\}\}(.*?)\{\{\/one\}\}/g, '$1')
        } else if (translation.includes('{{other}}')) {
          translation = translation.replace(/\{\{other\}\}(.*?)\{\{\/other\}\}/g, '$1')
        }
      }

      // Variable interpolation
      Object.entries(opts).forEach(([key, value]) => {
        const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
        translation = translation.replace(regex, String(value))
      })
    }

    return translation
  }, [translations])

  const isReady = !isLoading && !!translations && !error

  return {
    t,
    locale: locale as SupportedLocale,
    isLoading,
    isReady,
    error
  }
}

// Preload critical translations
export async function preloadTranslations(locale: SupportedLocale): Promise<void> {
  const cacheKey = locale

  if (!translationCache.get(cacheKey)) {
    try {
      const translationModule = await import(`@/locales/${locale}.json`)
      const translations = translationModule.default || translationModule
      translationCache.set(cacheKey, translations)
    } catch (error) {
      console.warn(`Failed to preload ${cacheKey}:`, error)
    }
  }
}

// Clear cache (useful for testing or memory management)
export function clearTranslationCache(): void {
  translationCache.clear()
}