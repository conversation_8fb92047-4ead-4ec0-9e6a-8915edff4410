import { createInstance } from 'i18next'
import resourcesToBackend from 'i18next-resources-to-backend'
import { initReactI18next } from 'react-i18next/initReactI18next'

const initI18next = async (locale: string, namespace: string = 'translation') => {
  const i18nInstance = createInstance()
  await i18nInstance
    .use(initReactI18next)
    .use(
      resourcesToBackend((language: string) =>
        import(`../locales/${language}.json`)
      )
    )
    .init({
      lng: locale,
      fallbackLng: 'zh',
      supportedLngs: ['zh', 'en'],
      defaultNS: 'translation',
      fallbackNS: 'translation',
      ns: 'translation',
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
      // 使用点号分隔的键名
      keySeparator: '.',
      nsSeparator: false,
    })
  return i18nInstance
}

export default initI18next