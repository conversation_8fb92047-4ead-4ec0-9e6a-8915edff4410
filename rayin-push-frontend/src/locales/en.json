{"common.welcome": "Welcome to <PERSON><PERSON>", "common.introduction": "Customizable Webhook message parsing and forwarding system", "common.description": "Description", "common.search": "Search", "common.create": "Create", "common.edit": "Edit", "common.delete": "Delete", "common.save": "Save", "common.saving": "Saving...", "common.cancel": "Cancel", "common.confirm": "Confirm", "common.loading": "Loading...", "common.error": "Error", "common.success": "Success", "common.failed": "Failed", "common.status": "Status", "common.allStatus": "All Status", "common.actions": "Actions", "common.name": "Name", "common.time": "Time", "common.type": "Type", "common.allTypes": "All Types", "common.enable": "Enable", "common.disable": "Disable", "common.enabled": "Enabled", "common.disabled": "Disabled", "common.token": "Token", "common.requestMethod": "Request Method", "common.operate": "Operate", "common.createdTime": "Created Time", "common.updatedTime": "Updated Time", "common.totalRecords": "Total {{count}} records", "common.selectedItems": "{{count}} selected", "common.batchOperation": "Batch Operation", "common.batchEnable": "Batch Enable", "common.batchDisable": "<PERSON>ch Disable", "common.perPage": "Per page", "common.itemsPerPage": "items", "common.firstPage": "First", "common.lastPage": "Last", "common.previousPage": "Previous", "common.nextPage": "Next", "common.page": "Page {{current}} of {{total}}", "common.noData": "No data", "common.auto": "Auto", "common.showing": "Showing {{from}} to {{to}} of {{total}} entries", "common.rowsPerPage": "Rows per page", "common.settings": "Settings", "common.language": "Language", "common.theme": "Theme", "common.light": "Light", "common.dark": "Dark", "navbar.dashboard": "Dashboard", "navbar.management": "Management", "navbar.config": "Interface Config", "navbar.channels": "Notification Channels", "navbar.logs": "Request Logs", "navbar.limits": "Request Limits", "navbar.system": "System", "navbar.users": "User Management", "navbar.monitoring": "System Monitoring", "navbar.analytics": "Analytics", "navbar.webhooks": "Webhook Management", "navbar.database": "Database Status", "dashboard.statistics": "Statistics", "dashboard.totalUsers": "Total Users", "dashboard.totalRequests": "Total Requests", "dashboard.successRate": "Success Rate", "dashboard.todayRequests": "Today's Requests", "dashboard.recentRequests": "Recent Requests", "dashboard.requestTrends": "Request Trends", "dashboard.last24Hours": "Last 24 Hours", "dashboard.noRecentRequests": "No recent requests", "dashboard.viewAllLogs": "View All Logs", "dashboard.refreshData": "Refresh Data", "dashboard.autoRefresh": "Auto Refresh", "dashboard.processing": "Processing", "dashboard.completed": "Completed", "dashboard.failed": "Failed", "dashboard.pending": "Pending", "dashboard.partial": "Partial Success", "dashboard.success": "Success", "dashboard.duration": "Duration", "dashboard.from": "From", "dashboard.channels": "Channels", "dashboard.viewDetails": "View Details", "dashboard.timeRange24h": "24 Hours", "dashboard.timeRange7d": "7 Days", "dashboard.timeRange30d": "30 Days", "config.title": "Interface Configuration", "config.createConfigDesc": "Create interface configuration", "config.editConfigDesc": "Edit interface configuration", "config.parsingRules": "Parsing Rules", "config.basicInfo": "Basic Information", "config.channelSettings": "Channel Settings", "config.configStatus": "Configuration Status", "config.searchPlaceholder": "Search configuration name or description...", "config.noConfigData": "No configuration data", "config.required": "Required", "config.configNamePlaceholder": "Enter configuration name", "config.configDescPlaceholder": "Enter configuration description", "config.selectMethodPlaceholder": "Select request method", "config.selectChannelPlaceholder": "Select push channel", "config.configNameRequired": "Configuration name is required", "config.requestMethodRequired": "Request method is required", "config.channelRequired": "At least one push channel must be selected", "config.selectMethodFirst": "Please select request method first", "config.selectMethodHint": "You need to select a request method before configuring parsing rules", "config.newParam": "New Parameter", "config.variable": "Variable", "config.messageExtractionRules": "Message Extraction Rules", "config.contentType": "Content Type", "config.variableMapping": "Variable Mapping", "config.addMapping": "Add Mapping", "config.noParameterMapping": "No parameter mapping, click \"Add Mapping\" to start configuration", "config.jsonPathPlaceholder": "JSON path (e.g.: user.name, info.address)", "config.interfaceParamPlaceholder": "Interface parameter name", "config.templateVariablePlaceholder": "Template variable name", "config.regexRules": "Regular Expression Rules", "config.addRule": "Add Rule", "config.noRegexRules": "No regex rules, click \"Add Rule\" to start configuration", "config.ruleNumber": "Rule {{number}}", "config.variableName": "Variable Name", "config.regexExpression": "Regular Expression", "config.templateVariableNamePlaceholder": "Variable name used in template", "config.regexPatternPlaceholder": "e.g.: My name is (.+)", "config.requestParamPlaceholder": "Request parameter", "config.variableNamePlaceholder": "Variable name", "config.regexVariableNamePlaceholder": "Variable name", "config.regexExpressionPlaceholder": "Regular expression", "config.formFieldMapping": "Configure form field mapping, map interface field names to template variable names", "config.jsonFieldMapping": "Configure JSON field path mapping, supports extracting multi-level fields", "config.regexExtraction": "Use regular expressions to extract data from text, each regex extracts one variable", "channels.title": "Notification Channels", "channels.createChannelDesc": "Create new notification channel configuration", "channels.editChannelDesc": "Edit existing notification channel configuration", "channels.channelName": "Channel Name", "channels.channelType": "Channel Type", "channels.channelDescription": "Channel Description", "channels.wechatBot": "WeChat Bot", "channels.feishuBot": "<PERSON><PERSON><PERSON>", "channels.customWebhook": "Custom Webhook", "channels.webhookUrl": "Webhook URL", "channels.httpMethod": "HTTP Method", "channels.requestHeaders": "Request Headers", "channels.requestBody": "Request Body", "channels.urlParameters": "URL Parameters", "channels.builtinTemplate": "Built-in Template", "channels.customTemplate": "Custom Template", "channels.templateVariables": "Template Variables", "channels.messageTemplate": "Message Template", "channels.testChannel": "Test Channel", "channels.testMessage": "Test Message", "channels.sendTest": "Send Test", "channels.testHistory": "Test History", "channels.channelStatus": "Channel Status", "channels.lastTestTime": "Last Test Time", "channels.testSuccess": "Test Success", "channels.testFailed": "Test Failed", "channels.variableHelp": "Variable Help", "channels.supportedVariables": "Supported Variables", "channels.conditionalLogic": "Conditional Logic", "channels.ifElseStatement": "if/else Statement", "channels.arithmeticExpression": "Arithmetic Expression", "channels.noChannelData": "No channel data available", "channels.basicInfo": "Basic Information", "channels.channelConfig": "Channel Configuration", "channels.channelNameRequired": "Channel name is required", "channels.channelTypeRequired": "Please select channel type", "channels.webhookUrlRequired": "Webhook URL is required", "channels.httpMethodRequired": "Please select HTTP method", "channels.messageTemplateRequired": "Message template is required", "channels.channelNamePlaceholder": "Enter channel name", "channels.channelDescPlaceholder": "Enter channel description", "channels.selectChannelTypePlaceholder": "Select channel type", "channels.webhookUrlPlaceholder": "Enter webhook URL", "channels.messageTemplatePlaceholder": "Enter message template", "channels.headerKeyPlaceholder": "Header name", "channels.headerValuePlaceholder": "Header value", "channels.builtinTemplateDesc": "Use built-in template with common variable replacements", "channels.variableHelpContent": "Supported variables: ${data} complete data, ${now} current time, ${title} message title, ${content} message content. Conditional statements: ${if condition}...${else}...${/if}. Arithmetic expressions: ${x1 + x2}", "channels.completeData": "Complete data", "channels.currentTime": "Current time", "channels.messageTitle": "Message title", "channels.messageContent": "Message content", "channels.searchPlaceholder": "Search channel name or description", "logs.title": "Request Logs", "logs.logDetails": "Log Details", "logs.requestInfo": "Request Info", "logs.responseInfo": "Response Info", "logs.channelResults": "Channel Results", "logs.originalData": "Original Data", "logs.processedData": "Processed Data", "logs.errorInfo": "Error <PERSON>", "logs.timeFilter": "Time Filter", "logs.statusFilter": "Status Filter", "logs.interfaceFilter": "Interface Filter", "logs.allInterfaces": "All Interfaces", "logs.requestId": "Request ID", "logs.interfaceName": "Interface Name", "logs.requestTime": "Request Time", "logs.responseTime": "Response Time", "logs.duration": "Duration", "logs.requestStatus": "Request Status", "logs.channelsCount": "Channels Count", "logs.successfulChannels": "Successful Channels", "logs.failedChannels": "Failed Channels", "logs.exportLogs": "Export Logs", "logs.exportCsv": "Export CSV", "logs.exportJson": "Export JSON", "logs.exportSelected": "Export Selected", "logs.exportAll": "Export All", "logs.exportProgress": "Export Progress", "logs.searchLogs": "Search Logs", "logs.searchPlaceholder": "Search request ID, interface name...", "logs.dateRange": "Date Range", "logs.today": "Today", "logs.yesterday": "Yesterday", "logs.last7Days": "Last 7 Days", "logs.last30Days": "Last 30 Days", "logs.customRange": "Custom Range", "limits.title": "Request Limits", "limits.rateLimits": "Rate Limits", "limits.ipLimits": "IP Limits", "limits.ruleName": "Rule Name", "limits.ruleDescription": "Rule Description", "limits.timeWindow": "Time Window", "limits.requestLimit": "Request Limit", "limits.ruleStatus": "Rule Status", "limits.ruleType": "Rule Type", "limits.globalLimit": "Global Limit", "limits.perIpLimit": "Per IP Limit", "limits.perUserLimit": "Per User Limit", "limits.seconds": "Seconds", "limits.minutes": "Minutes", "limits.hours": "Hours", "limits.days": "Days", "limits.requestsPerTimeWindow": "Requests per Time Window", "limits.ipAddress": "IP Address", "limits.ipRange": "IP Range", "limits.singleIp": "Single IP", "limits.ipWhitelist": "IP Whitelist", "limits.ipBlacklist": "IP Blacklist", "limits.addIpRule": "Add IP Rule", "limits.removeIpRule": "Remove IP Rule", "limits.currentLimits": "Current Limits", "limits.activeLimits": "Active Limits", "limits.limitStatistics": "Limit Statistics", "limits.triggeredCount": "Triggered Count", "limits.lastTriggered": "Last Triggered", "limits.affectedRequests": "Affected Requests", "limits.exemptIps": "Exempt IPs", "limits.exemptUsers": "Exempt Users", "limits.ruleHistory": "Rule History", "limits.limitExceeded": "Limit Exceeded", "limits.rateLimitReset": "Rate Limit Reset Time", "users.title": "User Management", "users.userName": "Username", "users.userEmail": "User Email", "users.userPassword": "User Password", "users.confirmPassword": "Confirm Password", "users.userRole": "User Role", "users.administrator": "Administrator", "users.regularUser": "Regular User", "users.userStatus": "User Status", "users.suspended": "Suspended", "users.createdTime": "Created Time", "users.lastLoginTime": "Last Login Time", "users.loginCount": "Login <PERSON>", "users.resetPassword": "Reset Password", "users.changePassword": "Change Password", "users.newPassword": "New Password", "users.currentPassword": "Current Password", "users.searchUsers": "Search Users", "users.searchPlaceholder": "Search username, email...", "users.roleFilter": "Role Filter", "users.statusFilter": "Status Filter", "users.allRoles": "All Roles", "users.userOperations": "User Operations", "users.operationLog": "Operation Log", "users.operationType": "Operation Type", "users.operationTime": "Operation Time", "users.operationDetails": "Operation Details", "users.loginHistory": "Login History", "users.loginIp": "Login IP", "users.loginDevice": "<PERSON><PERSON>", "users.cannotDeleteAdmin": "Cannot delete admin account", "users.cannotDeleteSelf": "Cannot delete your own account", "users.passwordMismatch": "Password mismatch", "users.userExists": "User already exists", "users.invalidEmail": "Invalid email format", "users.passwordTooWeak": "Password is too weak"}