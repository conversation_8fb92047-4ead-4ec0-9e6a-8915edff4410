{"title": "通知渠道", "createChannel": "创建渠道", "editChannel": "编辑渠道", "channelName": "渠道名称", "channelType": "渠道类型", "channelDescription": "渠道描述", "wechatBot": "微信机器人", "feishuBot": "飞书机器人", "customWebhook": "自定义Webhook", "webhookUrl": "Webhook地址", "httpMethod": "HTTP方法", "requestHeaders": "请求头", "requestBody": "请求体", "urlParameters": "URL参数", "builtinTemplate": "内置模板", "customTemplate": "自定义模板", "templateVariables": "模板变量", "messageTemplate": "消息模板", "testChannel": "测试渠道", "testMessage": "测试消息", "sendTest": "发送测试", "testHistory": "测试历史", "channelStatus": "渠道状态", "lastTestTime": "最后测试时间", "testSuccess": "测试成功", "testFailed": "测试失败", "variableHelp": "变量帮助", "supportedVariables": "支持的变量", "conditionalLogic": "条件逻辑", "ifElseStatement": "if/else语句", "arithmeticExpression": "算术表达式", "noChannelData": "暂无渠道数据", "basicInfo": "基本信息", "channelConfig": "渠道配置", "channelNameRequired": "渠道名称不能为空", "channelTypeRequired": "请选择渠道类型", "webhookUrlRequired": "Webhook地址不能为空", "httpMethodRequired": "请选择HTTP方法", "messageTemplateRequired": "消息模板不能为空", "channelNamePlaceholder": "请输入渠道名称", "channelDescPlaceholder": "请输入渠道描述", "selectChannelTypePlaceholder": "请选择渠道类型", "webhookUrlPlaceholder": "请输入Webhook地址", "messageTemplatePlaceholder": "请输入消息模板", "headerKeyPlaceholder": "请求头名称", "headerValuePlaceholder": "请求头值", "editChannelDesc": "编辑现有通知渠道的配置信息", "createChannelDesc": "创建新的通知渠道配置", "builtinTemplateDesc": "使用内置模板，支持常用变量替换", "variableHelpContent": "支持的变量：${data} 完整数据、${now} 当前时间、${title} 消息标题、${content} 消息内容。支持条件语句：${if condition}...${else}...${/if}。支持算术表达式：${x1 + x2}", "completeData": "完整数据", "currentTime": "当前时间", "messageTitle": "消息标题", "messageContent": "消息内容", "searchPlaceholder": "搜索渠道名称或描述"}