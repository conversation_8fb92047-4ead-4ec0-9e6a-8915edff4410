{"common.welcome": "欢迎使用 <PERSON><PERSON>", "common.introduction": "自定义 Webhook 消息解析转发系统", "common.description": "描述", "common.search": "搜索", "common.create": "创建", "common.edit": "编辑", "common.delete": "删除", "common.save": "保存", "common.saving": "保存中...", "common.cancel": "取消", "common.confirm": "确认", "common.loading": "加载中...", "common.error": "错误", "common.success": "成功", "common.failed": "失败", "common.status": "状态", "common.allStatus": "全部状态", "common.actions": "操作", "common.name": "名称", "common.time": "时间", "common.type": "类型", "common.allTypes": "全部类型", "common.enable": "启用", "common.disable": "禁用", "common.enabled": "已启用", "common.disabled": "已禁用", "common.token": "令牌", "common.requestMethod": "请求方法", "common.operate": "操作", "common.createdTime": "创建时间", "common.updatedTime": "更新时间", "common.totalRecords": "共 {{count}} 条记录", "common.selectedItems": "已选择 {{count}} 项", "common.batchOperation": "批量操作", "common.batchEnable": "批量启用", "common.batchDisable": "批量禁用", "common.perPage": "每页", "common.itemsPerPage": "条", "common.firstPage": "首页", "common.lastPage": "尾页", "common.previousPage": "上一页", "common.nextPage": "下一页", "common.page": "第 {{current}} 页，共 {{total}} 页", "common.noData": "暂无数据", "common.auto": "自动", "common.showing": "显示 {{from}} 到 {{to}} 条，共 {{total}} 条", "common.rowsPerPage": "每页行数", "common.settings": "设置", "common.language": "语言", "common.theme": "主题", "common.light": "浅色", "common.dark": "深色", "navbar.dashboard": "仪表盘", "navbar.management": "管理", "navbar.config": "接口配置", "navbar.channels": "通知渠道", "navbar.logs": "请求日志", "navbar.limits": "请求限制", "navbar.system": "系统", "navbar.users": "用户管理", "navbar.monitoring": "系统监控", "navbar.analytics": "数据分析", "navbar.webhooks": "Webhook管理", "navbar.database": "数据库状态", "dashboard.statistics": "统计信息", "dashboard.totalUsers": "总用户数", "dashboard.totalRequests": "总请求数", "dashboard.successRate": "成功率", "dashboard.todayRequests": "今日请求", "dashboard.recentRequests": "最近请求", "dashboard.requestTrends": "请求趋势", "dashboard.last24Hours": "最近24小时", "dashboard.noRecentRequests": "暂无最近请求", "dashboard.viewAllLogs": "查看所有日志", "dashboard.refreshData": "刷新数据", "dashboard.autoRefresh": "自动刷新", "dashboard.processing": "处理中", "dashboard.completed": "已完成", "dashboard.failed": "失败", "dashboard.pending": "待处理", "dashboard.partial": "部分成功", "dashboard.success": "成功", "dashboard.duration": "耗时", "dashboard.from": "来自", "dashboard.channels": "渠道", "dashboard.viewDetails": "查看详情", "dashboard.timeRange24h": "24小时", "dashboard.timeRange7d": "7天", "dashboard.timeRange30d": "30天", "config.title": "接口配置", "config.createConfigDesc": "创建接口配置", "config.editConfigDesc": "编辑接口配置", "config.parsingRules": "解析规则", "config.basicInfo": "基本信息", "config.channelSettings": "渠道设置", "config.configStatus": "配置状态", "config.searchPlaceholder": "搜索配置名称或描述...", "config.noConfigData": "暂无配置数据", "config.required": "必填", "config.configNamePlaceholder": "请输入配置名称", "config.configDescPlaceholder": "请输入配置描述", "config.selectMethodPlaceholder": "请选择请求方式", "config.selectChannelPlaceholder": "请选择推送渠道", "config.configNameRequired": "配置名称不能为空", "config.requestMethodRequired": "请求方式不能为空", "config.channelRequired": "请至少选择一个推送渠道", "config.selectMethodFirst": "请先选择请求方式", "config.selectMethodHint": "需要选择请求方式后才能配置解析规则", "config.newParam": "新参数", "config.variable": "变量", "config.messageExtractionRules": "消息提取规则", "config.contentType": "内容类型", "config.variableMapping": "变量映射", "config.addMapping": "添加映射", "config.noParameterMapping": "暂无参数映射，点击\"添加映射\"开始配置", "config.jsonPathPlaceholder": "JSON路径 (如: user.name, info.address)", "config.interfaceParamPlaceholder": "接口参数名", "config.templateVariablePlaceholder": "模板变量名", "config.regexRules": "正则表达式规则", "config.addRule": "添加规则", "config.noRegexRules": "暂无正则表达式规则，点击\"添加规则\"开始配置", "config.ruleNumber": "规则 {{number}}", "config.variableName": "变量名", "config.regexExpression": "正则表达式", "config.templateVariableNamePlaceholder": "模板中使用的变量名", "config.regexPatternPlaceholder": "如: 我叫(.+)", "config.requestParamPlaceholder": "请求参数", "config.variableNamePlaceholder": "变量名", "config.regexVariableNamePlaceholder": "变量名", "config.regexExpressionPlaceholder": "正则表达式", "config.formFieldMapping": "配置表单字段映射，将接口传递的字段名映射为模板中使用的变量名", "config.jsonFieldMapping": "配置 json 字段路径映射，支持提取多层级的字段", "config.regexExtraction": "使用正则表达式从文本中提取数据，每个正则表达式提取一个变量", "channels.title": "通知渠道", "channels.createChannelDesc": "创建新的通知渠道配置", "channels.editChannelDesc": "编辑现有通知渠道的配置信息", "channels.channelName": "渠道名称", "channels.channelType": "渠道类型", "channels.channelDescription": "渠道描述", "channels.wechatBot": "微信机器人", "channels.feishuBot": "飞书机器人", "channels.customWebhook": "自定义Webhook", "channels.webhookUrl": "Webhook地址", "channels.httpMethod": "HTTP方法", "channels.requestHeaders": "请求头", "channels.requestBody": "请求体", "channels.urlParameters": "URL参数", "channels.builtinTemplate": "内置模板", "channels.customTemplate": "自定义模板", "channels.templateVariables": "模板变量", "channels.messageTemplate": "消息模板", "channels.testChannel": "测试渠道", "channels.testMessage": "测试消息", "channels.sendTest": "发送测试", "channels.testHistory": "测试历史", "channels.channelStatus": "渠道状态", "channels.lastTestTime": "最后测试时间", "channels.testSuccess": "测试成功", "channels.testFailed": "测试失败", "channels.variableHelp": "变量帮助", "channels.supportedVariables": "支持的变量", "channels.conditionalLogic": "条件逻辑", "channels.ifElseStatement": "if/else语句", "channels.arithmeticExpression": "算术表达式", "channels.noChannelData": "暂无渠道数据", "channels.basicInfo": "基本信息", "channels.channelConfig": "渠道配置", "channels.channelNameRequired": "渠道名称不能为空", "channels.channelTypeRequired": "请选择渠道类型", "channels.webhookUrlRequired": "Webhook地址不能为空", "channels.httpMethodRequired": "请选择HTTP方法", "channels.messageTemplateRequired": "消息模板不能为空", "channels.channelNamePlaceholder": "请输入渠道名称", "channels.channelDescPlaceholder": "请输入渠道描述", "channels.selectChannelTypePlaceholder": "请选择渠道类型", "channels.webhookUrlPlaceholder": "请输入Webhook地址", "channels.messageTemplatePlaceholder": "请输入消息模板", "channels.headerKeyPlaceholder": "请求头名称", "channels.headerValuePlaceholder": "请求头值", "channels.builtinTemplateDesc": "使用内置模板，支持常用变量替换", "channels.variableHelpContent": "支持的变量：${data} 完整数据、${now} 当前时间、${title} 消息标题、${content} 消息内容。支持条件语句：${if condition}...${else}...${/if}。支持算术表达式：${x1 + x2}", "channels.completeData": "完整数据", "channels.currentTime": "当前时间", "channels.messageTitle": "消息标题", "channels.messageContent": "消息内容", "channels.searchPlaceholder": "搜索渠道名称或描述", "logs.title": "请求日志", "logs.logDetails": "日志详情", "logs.requestInfo": "请求信息", "logs.responseInfo": "响应信息", "logs.channelResults": "渠道结果", "logs.originalData": "原始数据", "logs.processedData": "处理后数据", "logs.errorInfo": "错误信息", "logs.timeFilter": "时间筛选", "logs.statusFilter": "状态筛选", "logs.interfaceFilter": "接口筛选", "logs.allInterfaces": "全部接口", "logs.requestId": "请求ID", "logs.interfaceName": "接口名称", "logs.requestTime": "请求时间", "logs.responseTime": "响应时间", "logs.duration": "耗时", "logs.requestStatus": "请求状态", "logs.channelsCount": "渠道数量", "logs.successfulChannels": "成功渠道", "logs.failedChannels": "失败渠道", "logs.exportLogs": "导出日志", "logs.exportCsv": "导出CSV", "logs.exportJson": "导出JSON", "logs.exportSelected": "导出选中", "logs.exportAll": "导出全部", "logs.exportProgress": "导出进度", "logs.searchLogs": "搜索日志", "logs.searchPlaceholder": "搜索请求ID、接口名称...", "logs.dateRange": "日期范围", "logs.today": "今天", "logs.yesterday": "昨天", "logs.last7Days": "最近7天", "logs.last30Days": "最近30天", "logs.customRange": "自定义范围", "limits.title": "请求限制", "limits.rateLimits": "速率限制", "limits.ipLimits": "IP限制", "limits.ruleName": "规则名称", "limits.ruleDescription": "规则描述", "limits.timeWindow": "时间窗口", "limits.requestLimit": "请求限制", "limits.ruleStatus": "规则状态", "limits.ruleType": "规则类型", "limits.globalLimit": "全局限制", "limits.perIpLimit": "单IP限制", "limits.perUserLimit": "单用户限制", "limits.seconds": "秒", "limits.minutes": "分钟", "limits.hours": "小时", "limits.days": "天", "limits.requestsPerTimeWindow": "每时间窗口请求数", "limits.ipAddress": "IP地址", "limits.ipRange": "IP段", "limits.singleIp": "单个IP", "limits.ipWhitelist": "IP白名单", "limits.ipBlacklist": "IP黑名单", "limits.addIpRule": "添加IP规则", "limits.removeIpRule": "移除IP规则", "limits.currentLimits": "当前限制", "limits.activeLimits": "生效中的限制", "limits.limitStatistics": "限制统计", "limits.triggeredCount": "触发次数", "limits.lastTriggered": "最后触发时间", "limits.affectedRequests": "受影响请求", "limits.exemptIps": "豁免IP", "limits.exemptUsers": "豁免用户", "limits.ruleHistory": "规则历史", "limits.limitExceeded": "超出限制", "limits.rateLimitReset": "限制重置时间", "users.title": "用户管理", "users.userName": "用户名", "users.userEmail": "用户邮箱", "users.userPassword": "用户密码", "users.confirmPassword": "确认密码", "users.userRole": "用户角色", "users.administrator": "管理员", "users.regularUser": "普通用户", "users.userStatus": "用户状态", "users.suspended": "已暂停", "users.createdTime": "创建时间", "users.lastLoginTime": "最后登录时间", "users.loginCount": "登录次数", "users.resetPassword": "重置密码", "users.changePassword": "修改密码", "users.newPassword": "新密码", "users.currentPassword": "当前密码", "users.searchUsers": "搜索用户", "users.searchPlaceholder": "搜索用户名、邮箱...", "users.roleFilter": "角色筛选", "users.statusFilter": "状态筛选", "users.allRoles": "全部角色", "users.userOperations": "用户操作", "users.operationLog": "操作日志", "users.operationType": "操作类型", "users.operationTime": "操作时间", "users.operationDetails": "操作详情", "users.loginHistory": "登录历史", "users.loginIp": "登录IP", "users.loginDevice": "登录设备", "users.cannotDeleteAdmin": "无法删除管理员账户", "users.cannotDeleteSelf": "无法删除自己的账户", "users.passwordMismatch": "密码不匹配", "users.userExists": "用户已存在", "users.invalidEmail": "邮箱格式无效", "users.passwordTooWeak": "密码强度不够"}