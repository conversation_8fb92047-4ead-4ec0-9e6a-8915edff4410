{"common": {"welcome": "欢迎使用 <PERSON><PERSON>", "introduction": "自定义 Webhook 消息解析转发系统", "description": "描述", "search": "搜索", "create": "创建", "edit": "编辑", "delete": "删除", "save": "保存", "saving": "保存中...", "cancel": "取消", "confirm": "确认", "loading": "加载中...", "error": "错误", "success": "成功", "failed": "失败", "status": "状态", "allStatus": "全部状态", "actions": "操作", "name": "名称", "time": "时间", "type": "类型", "allTypes": "全部类型", "enable": "启用", "disable": "禁用", "enabled": "已启用", "disabled": "已禁用", "token": "令牌", "requestMethod": "请求方法", "operate": "操作", "createdTime": "创建时间", "updatedTime": "更新时间", "totalRecords": "共 {{count}} 条记录", "selectedItems": "已选择 {{count}} 项", "batchOperation": "批量操作", "batchEnable": "批量启用", "batchDisable": "批量禁用", "perPage": "每页", "itemsPerPage": "条", "firstPage": "首页", "lastPage": "尾页", "previousPage": "上一页", "nextPage": "下一页", "page": "第 {{current}} 页，共 {{total}} 页", "noData": "暂无数据", "auto": "自动", "showing": "显示 {{from}} 到 {{to}} 条，共 {{total}} 条", "rowsPerPage": "每页行数", "settings": "设置", "language": "语言", "theme": "主题", "light": "浅色", "dark": "深色"}, "navbar": {"dashboard": "仪表盘", "management": "管理", "config": "接口配置", "channels": "通知渠道", "logs": "请求日志", "limits": "请求限制", "system": "系统", "users": "用户管理", "monitoring": "系统监控", "analytics": "数据分析", "webhooks": "Webhook管理", "database": "数据库状态"}, "dashboard": {"statistics": "统计信息", "totalUsers": "总用户数", "totalRequests": "总请求数", "successRate": "成功率", "todayRequests": "今日请求", "recentRequests": "最近请求", "requestTrends": "请求趋势", "last24Hours": "最近24小时", "noRecentRequests": "暂无最近请求", "viewAllLogs": "查看所有日志", "refreshData": "刷新数据", "autoRefresh": "自动刷新", "processing": "处理中", "completed": "已完成", "failed": "失败", "pending": "待处理", "partial": "部分成功", "success": "成功", "duration": "耗时", "from": "来自", "channels": "渠道", "viewDetails": "查看详情", "timeRange24h": "24小时", "timeRange7d": "7天", "timeRange30d": "30天"}, "config": {"title": "接口配置", "createConfigDesc": "创建接口配置", "editConfigDesc": "编辑接口配置", "parsingRules": "解析规则", "basicInfo": "基本信息", "channelSettings": "渠道设置", "configStatus": "配置状态", "searchPlaceholder": "搜索配置名称或描述...", "noConfigData": "暂无配置数据", "required": "必填", "configNamePlaceholder": "请输入配置名称", "configDescPlaceholder": "请输入配置描述", "selectMethodPlaceholder": "请选择请求方式", "selectChannelPlaceholder": "请选择推送渠道", "configNameRequired": "配置名称不能为空", "requestMethodRequired": "请求方式不能为空", "channelRequired": "请至少选择一个推送渠道", "selectMethodFirst": "请先选择请求方式", "selectMethodHint": "需要选择请求方式后才能配置解析规则", "newParam": "新参数", "variable": "变量", "messageExtractionRules": "消息提取规则", "contentType": "内容类型", "variableMapping": "变量映射", "addMapping": "添加映射", "noParameterMapping": "暂无参数映射，点击\"添加映射\"开始配置", "jsonPathPlaceholder": "JSON路径 (如: user.name, info.address)", "interfaceParamPlaceholder": "接口参数名", "templateVariablePlaceholder": "模板变量名", "regexRules": "正则表达式规则", "addRule": "添加规则", "noRegexRules": "暂无正则表达式规则，点击\"添加规则\"开始配置", "ruleNumber": "规则 {{number}}", "variableName": "变量名", "regexExpression": "正则表达式", "templateVariableNamePlaceholder": "模板中使用的变量名", "regexPatternPlaceholder": "如: 我叫(.+)", "requestParamPlaceholder": "请求参数", "variableNamePlaceholder": "变量名", "regexVariableNamePlaceholder": "变量名", "regexExpressionPlaceholder": "正则表达式", "formFieldMapping": "配置表单字段映射，将接口传递的字段名映射为模板中使用的变量名", "jsonFieldMapping": "配置 json 字段路径映射，支持提取多层级的字段", "regexExtraction": "使用正则表达式从文本中提取数据，每个正则表达式提取一个变量"}, "channels": {"title": "通知渠道", "createChannelDesc": "创建新的通知渠道配置", "editChannelDesc": "编辑现有通知渠道的配置信息", "channelName": "渠道名称", "channelType": "渠道类型", "channelDescription": "渠道描述", "wechatBot": "微信机器人", "feishuBot": "飞书机器人", "customWebhook": "自定义Webhook", "webhookUrl": "Webhook地址", "httpMethod": "HTTP方法", "requestHeaders": "请求头", "requestBody": "请求体", "urlParameters": "URL参数", "builtinTemplate": "内置模板", "customTemplate": "自定义模板", "templateVariables": "模板变量", "messageTemplate": "消息模板", "testChannel": "测试渠道", "testMessage": "测试消息", "sendTest": "发送测试", "testHistory": "测试历史", "channelStatus": "渠道状态", "lastTestTime": "最后测试时间", "testSuccess": "测试成功", "testFailed": "测试失败", "variableHelp": "变量帮助", "supportedVariables": "支持的变量", "conditionalLogic": "条件逻辑", "ifElseStatement": "if/else语句", "arithmeticExpression": "算术表达式", "noChannelData": "暂无渠道数据", "basicInfo": "基本信息", "channelConfig": "渠道配置", "channelNameRequired": "渠道名称不能为空", "channelTypeRequired": "请选择渠道类型", "webhookUrlRequired": "Webhook地址不能为空", "httpMethodRequired": "请选择HTTP方法", "messageTemplateRequired": "消息模板不能为空", "channelNamePlaceholder": "请输入渠道名称", "channelDescPlaceholder": "请输入渠道描述", "selectChannelTypePlaceholder": "请选择渠道类型", "webhookUrlPlaceholder": "请输入Webhook地址", "messageTemplatePlaceholder": "请输入消息模板", "headerKeyPlaceholder": "请求头名称", "headerValuePlaceholder": "请求头值", "builtinTemplateDesc": "使用内置模板，支持常用变量替换", "variableHelpContent": "支持的变量：${data} 完整数据、${now} 当前时间、${title} 消息标题、${content} 消息内容。支持条件语句：${if condition}...${else}...${/if}。支持算术表达式：${x1 + x2}", "completeData": "完整数据", "currentTime": "当前时间", "messageTitle": "消息标题", "messageContent": "消息内容", "searchPlaceholder": "搜索渠道名称或描述"}, "logs": {"title": "请求日志", "logDetails": "日志详情", "requestInfo": "请求信息", "responseInfo": "响应信息", "channelResults": "渠道结果", "originalData": "原始数据", "processedData": "处理后数据", "errorInfo": "错误信息", "timeFilter": "时间筛选", "statusFilter": "状态筛选", "interfaceFilter": "接口筛选", "allInterfaces": "全部接口", "requestId": "请求ID", "interfaceName": "接口名称", "requestTime": "请求时间", "responseTime": "响应时间", "duration": "耗时", "requestStatus": "请求状态", "channelsCount": "渠道数量", "successfulChannels": "成功渠道", "failedChannels": "失败渠道", "exportLogs": "导出日志", "exportCsv": "导出CSV", "exportJson": "导出JSON", "exportSelected": "导出选中", "exportAll": "导出全部", "exportProgress": "导出进度", "searchLogs": "搜索日志", "searchPlaceholder": "搜索请求ID、接口名称...", "dateRange": "日期范围", "today": "今天", "yesterday": "昨天", "last7Days": "最近7天", "last30Days": "最近30天", "customRange": "自定义范围"}, "limits": {"title": "请求限制", "rateLimits": "速率限制", "ipLimits": "IP限制", "ruleName": "规则名称", "ruleDescription": "规则描述", "timeWindow": "时间窗口", "requestLimit": "请求限制", "ruleStatus": "规则状态", "ruleType": "规则类型", "globalLimit": "全局限制", "perIpLimit": "单IP限制", "perUserLimit": "单用户限制", "seconds": "秒", "minutes": "分钟", "hours": "小时", "days": "天", "requestsPerTimeWindow": "每时间窗口请求数", "ipAddress": "IP地址", "ipRange": "IP段", "singleIp": "单个IP", "ipWhitelist": "IP白名单", "ipBlacklist": "IP黑名单", "addIpRule": "添加IP规则", "removeIpRule": "移除IP规则", "currentLimits": "当前限制", "activeLimits": "生效中的限制", "limitStatistics": "限制统计", "triggeredCount": "触发次数", "lastTriggered": "最后触发时间", "affectedRequests": "受影响请求", "exemptIps": "豁免IP", "exemptUsers": "豁免用户", "ruleHistory": "规则历史", "limitExceeded": "超出限制", "rateLimitReset": "限制重置时间"}, "users": {"title": "用户管理", "userName": "用户名", "userEmail": "用户邮箱", "userPassword": "用户密码", "confirmPassword": "确认密码", "userRole": "用户角色", "administrator": "管理员", "regularUser": "普通用户", "userStatus": "用户状态", "suspended": "已暂停", "createdTime": "创建时间", "lastLoginTime": "最后登录时间", "loginCount": "登录次数", "resetPassword": "重置密码", "changePassword": "修改密码", "newPassword": "新密码", "currentPassword": "当前密码", "searchUsers": "搜索用户", "searchPlaceholder": "搜索用户名、邮箱...", "roleFilter": "角色筛选", "statusFilter": "状态筛选", "allRoles": "全部角色", "userOperations": "用户操作", "operationLog": "操作日志", "operationType": "操作类型", "operationTime": "操作时间", "operationDetails": "操作详情", "loginHistory": "登录历史", "loginIp": "登录IP", "loginDevice": "登录设备", "cannotDeleteAdmin": "无法删除管理员账户", "cannotDeleteSelf": "无法删除自己的账户", "passwordMismatch": "密码不匹配", "userExists": "用户已存在", "invalidEmail": "邮箱格式无效", "passwordTooWeak": "密码强度不够"}}