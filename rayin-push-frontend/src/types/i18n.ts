// 通用翻译类型
export interface CommonTranslations {
  welcome: string
  introduction: string
  description: string
  dashboard: string
  config: string
  channels: string
  logs: string
  limits: string
  users: string
  settings: string
  language: string
  theme: string
  light: string
  dark: string
  search: string
  create: string
  edit: string
  delete: string
  save: string
  saving: string
  cancel: string
  confirm: string
  loading: string
  error: string
  success: string
  failed: string
  status: string
  allStatus: string
  actions: string
  name: string
  time: string
  type: string
  allTypes: string
  enable: string
  disable: string
  enabled: string
  disabled: string
  token: string
  requestMethod: string
  management: string
  system: string
  monitoring: string
  analytics: string
  webhooks: string
  database: string
  operate: string
  createdTime: string
  updatedTime: string
  totalRecords: string
  selectedItems: string
  batchOperation: string
  batchEnable: string
  batchDisable: string
  perPage: string
  itemsPerPage: string
  firstPage: string
  lastPage: string
  previousPage: string
  nextPage: string
  page: string
  noData: string
  auto: string
  showing: string
  rowsPerPage: string
}

// 仪表盘翻译类型
export interface DashboardTranslations {
  title: string
  statistics: string
  totalUsers: string
  totalRequests: string
  successRate: string
  todayRequests: string
  activeConfigs: string
  activeChannels: string
  recentRequests: string
  requestTrends: string
  last24Hours: string
  last7Days: string
  last30Days: string
  noRecentRequests: string
  viewAllLogs: string
  refreshData: string
  autoRefresh: string
  interface: string
  requestTime: string
  processing: string
  completed: string
  failed: string
  pending: string
  partial: string
  success: string
  duration: string
  from: string
  channels: string
  viewDetails: string
  timeRange24h: string
  timeRange7d: string
  timeRange30d: string
}

// 接口配置翻译类型
export interface ConfigTranslations {
  title: string
  createConfig: string
  createConfigDesc: string
  editConfig: string
  editConfigDesc: string
  configName: string
  configToken: string
  configDescription: string
  interfaceUrl: string
  requestMethod: string
  pushChannels: string
  parsingRules: string
  basicInfo: string
  channelSettings: string
  ruleSettings: string
  testConfig: string
  getRequest: string
  postForm: string
  postJson: string
  plainText: string
  parameterMapping: string
  fieldMapping: string
  jsonPath: string
  regexPattern: string
  parsingPreview: string
  testData: string
  testResult: string
  configStatus: string
  usageCount: string
  copyConfig: string
  exportConfig: string
  importConfig: string
  searchPlaceholder: string
  noConfigData: string
  required: string
  configNamePlaceholder: string
  configDescPlaceholder: string
  selectMethodPlaceholder: string
  selectChannelPlaceholder: string
  configNameRequired: string
  requestMethodRequired: string
  channelRequired: string
  selectMethodFirst: string
  selectMethodHint: string
  saving: string
  newParam: string
  variable: string
  messageExtractionRules: string
  contentType: string
  variableMapping: string
  addMapping: string
  noParameterMapping: string
  jsonPathPlaceholder: string
  interfaceParamPlaceholder: string
  templateVariablePlaceholder: string
  regexRules: string
  addRule: string
  noRegexRules: string
  ruleNumber: string
  variableName: string
  regexExpression: string
  templateVariableNamePlaceholder: string
  regexPatternPlaceholder: string
  requestParamPlaceholder: string
  variableNamePlaceholder: string
  regexVariableNamePlaceholder: string
  regexExpressionPlaceholder: string
  formFieldMapping: string
  jsonFieldMapping: string
  regexExtraction: string
}

// 通知渠道翻译类型
export interface ChannelsTranslations {
  title: string
  channelName: string
  channelType: string
  channelDescription: string
  wechatBot: string
  feishuBot: string
  customWebhook: string
  webhookUrl: string
  httpMethod: string
  requestHeaders: string
  requestBody: string
  urlParameters: string
  builtinTemplate: string
  customTemplate: string
  templateVariables: string
  messageTemplate: string
  testChannel: string
  testMessage: string
  sendTest: string
  testHistory: string
  channelStatus: string
  lastTestTime: string
  testSuccess: string
  testFailed: string
  variableHelp: string
  supportedVariables: string
  conditionalLogic: string
  ifElseStatement: string
  arithmeticExpression: string
}

// 请求日志翻译类型
export interface LogsTranslations {
  title: string
  logDetails: string
  requestInfo: string
  responseInfo: string
  channelResults: string
  originalData: string
  processedData: string
  errorInfo: string
  timeFilter: string
  statusFilter: string
  interfaceFilter: string

  allInterfaces: string
  requestId: string
  interfaceName: string
  requestTime: string
  responseTime: string
  duration: string
  requestStatus: string
  channelsCount: string
  successfulChannels: string
  failedChannels: string
  exportLogs: string
  exportCsv: string
  exportJson: string
  exportSelected: string
  exportAll: string
  exportProgress: string
  searchLogs: string
  searchPlaceholder: string
  dateRange: string
  today: string
  yesterday: string
  last7Days: string
  last30Days: string
  customRange: string
}

// 请求限制翻译类型
export interface LimitsTranslations {
  title: string
  rateLimits: string
  ipLimits: string

  ruleName: string
  ruleDescription: string
  timeWindow: string
  requestLimit: string
  ruleStatus: string
  ruleType: string
  globalLimit: string
  perIpLimit: string
  perUserLimit: string
  seconds: string
  minutes: string
  hours: string
  days: string
  requestsPerTimeWindow: string
  ipAddress: string
  ipRange: string
  singleIp: string
  ipWhitelist: string
  ipBlacklist: string
  addIpRule: string
  removeIpRule: string
  currentLimits: string
  activeLimits: string
  limitStatistics: string
  triggeredCount: string
  lastTriggered: string
  affectedRequests: string
  exemptIps: string
  exemptUsers: string

  ruleHistory: string
  limitExceeded: string
  rateLimitReset: string
}

// 用户管理翻译类型
export interface UsersTranslations {
  title: string

  userName: string
  userEmail: string
  userPassword: string
  confirmPassword: string
  userRole: string
  administrator: string
  regularUser: string
  userStatus: string

  suspended: string
  createdTime: string
  lastLoginTime: string
  loginCount: string
  resetPassword: string
  changePassword: string
  newPassword: string
  currentPassword: string

  searchUsers: string
  searchPlaceholder: string
  roleFilter: string
  statusFilter: string
  allRoles: string

  userOperations: string
  operationLog: string
  operationType: string
  operationTime: string
  operationDetails: string
  loginHistory: string
  loginIp: string
  loginDevice: string
  cannotDeleteAdmin: string
  cannotDeleteSelf: string
  passwordMismatch: string
  userExists: string
  invalidEmail: string
  passwordTooWeak: string
}

// 支持的语言类型
export type SupportedLocale = 'zh' | 'en'

// 导航翻译类型
export interface NavigationTranslations {
  dashboard: string
  management: string
  config: string
  channels: string
  logs: string
  limits: string
  system: string
  users: string
  monitoring: string
  analytics: string
  webhooks: string
  database: string
}

// 翻译命名空间类型
export type TranslationNamespace = 
  | 'common'
  | 'dashboard'
  | 'config'
  | 'channels'
  | 'logs'
  | 'limits'
  | 'users'
  | 'navigation'

// 翻译类型映射
export interface TranslationTypeMap {
  common: CommonTranslations
  dashboard: DashboardTranslations
  config: ConfigTranslations
  channels: ChannelsTranslations
  logs: LogsTranslations
  limits: LimitsTranslations
  users: UsersTranslations
  navigation: NavigationTranslations
}