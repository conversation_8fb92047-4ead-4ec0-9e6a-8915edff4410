#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

function validateNestedStructure() {
  console.log('🔍 Validating nested i18n structure...\n')
  
  const zhPath = path.join(__dirname, '..', 'src', 'locales', 'zh.json')
  const enPath = path.join(__dirname, '..', 'src', 'locales', 'en.json')
  
  const zh = JSON.parse(fs.readFileSync(zhPath, 'utf8'))
  const en = JSON.parse(fs.readFileSync(enPath, 'utf8'))
  
  console.log('📊 Structure overview:')
  console.log('Chinese namespaces:', Object.keys(zh))
  console.log('English namespaces:', Object.keys(en))
  console.log()
  
  // 检查结构一致性
  const zhNamespaces = Object.keys(zh).sort()
  const enNamespaces = Object.keys(en).sort()
  const consistent = JSON.stringify(zhNamespaces) === JSON.stringify(enNamespaces)
  
  console.log('Structure consistency:', consistent ? '✅ PASS' : '❌ FAIL')
  console.log()
  
  // 检查每个命名空间的键数量
  console.log('📋 Namespace key counts:')
  let allMatch = true
  
  zhNamespaces.forEach(ns => {
    const zhKeys = Object.keys(zh[ns] || {}).length
    const enKeys = Object.keys(en[ns] || {}).length
    const match = zhKeys === enKeys
    
    if (!match) allMatch = false
    
    console.log(`  ${ns}: zh=${zhKeys}, en=${enKeys} ${match ? '✅' : '❌'}`)
  })
  
  console.log()
  
  // 检查缺失的键
  console.log('🔍 Checking for missing keys:')
  let hasMissingKeys = false
  
  zhNamespaces.forEach(ns => {
    if (!zh[ns] || !en[ns]) return
    
    const zhKeys = Object.keys(zh[ns])
    const enKeys = Object.keys(en[ns])
    
    const missingInEn = zhKeys.filter(key => !enKeys.includes(key))
    const missingInZh = enKeys.filter(key => !zhKeys.includes(key))
    
    if (missingInEn.length > 0) {
      hasMissingKeys = true
      console.log(`  ❌ ${ns}: Missing in English:`, missingInEn)
    }
    
    if (missingInZh.length > 0) {
      hasMissingKeys = true
      console.log(`  ❌ ${ns}: Missing in Chinese:`, missingInZh)
    }
  })
  
  if (!hasMissingKeys) {
    console.log('  ✅ No missing keys found')
  }
  
  console.log()
  
  // 总结
  const allValid = consistent && allMatch && !hasMissingKeys
  
  if (allValid) {
    console.log('🎉 All validations passed!')
    console.log('✅ Structure is consistent')
    console.log('✅ Key counts match')
    console.log('✅ No missing keys')
  } else {
    console.log('❌ Validation failed!')
    if (!consistent) console.log('  - Structure inconsistency detected')
    if (!allMatch) console.log('  - Key count mismatches found')
    if (hasMissingKeys) console.log('  - Missing keys detected')
  }
  
  return allValid
}

// 运行验证
const isValid = validateNestedStructure()
process.exit(isValid ? 0 : 1)
